# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
vendor/
tmp/

# Go workspace file
go.work

# System Files
.DS_Store
Thumbs.db

# Custom
config/release/*.yml
public/assets/qqwry.ipdb
public/storage
storage/app/*
!storage/app/.gitkeep
build/*
docs/*.md
