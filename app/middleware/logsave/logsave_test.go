package logsave

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
)

func TestAuthMiddleware_ExtractUserNameFromRequestBody(t *testing.T) {
	// 设置 Gin 为测试模式
	gin.SetMode(gin.TestMode)

	// 创建测试用的请求体
	loginReq := map[string]interface{}{
		"userName": "testuser",
		"password": "testpass",
	}
	reqBody, _ := json.Marshal(loginReq)

	// 创建 HTTP 请求
	req, _ := http.NewRequest("POST", "/auth/login", bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")

	// 创建响应记录器
	w := httptest.NewRecorder()

	// 创建 Gin 上下文
	router := gin.New()

	// 用于捕获从中间件提取的用户名
	var capturedUserName string

	// 添加测试中间件来捕获用户名
	router.Use(func(ctx *gin.Context) {
		// 模拟 Auth 中间件的逻辑
		requestMethod := ctx.Request.Method
		if requestMethod != "" && requestMethod != "GET" {
			// 模拟 GetNameBySession 返回空字符串（未登录状态）
			userName := ""

			// 读取请求体
			var bodyBytes []byte
			if ctx.Request.Body != nil {
				bodyBytes, _ = io.ReadAll(ctx.Request.Body)
			}
			ctx.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

			// 如果从session获取不到用户名，尝试从请求体中获取
			if userName == "" {
				var reqParams map[string]any
				if err := json.Unmarshal(bodyBytes, &reqParams); err == nil {
					if userNameFromReq, ok := reqParams["userName"]; ok {
						if userNameStr, ok := userNameFromReq.(string); ok {
							userName = userNameStr
						}
					}
				}
			}

			capturedUserName = userName
		}
		ctx.Next()
	})

	// 添加测试路由
	router.POST("/auth/login", func(ctx *gin.Context) {
		ctx.JSON(200, gin.H{"message": "login endpoint"})
	})

	// 执行请求
	router.ServeHTTP(w, req)

	// 验证结果
	if capturedUserName != "testuser" {
		t.Errorf("期望用户名为 'testuser'，实际得到 '%s'", capturedUserName)
	}
	if w.Code != 200 {
		t.Errorf("期望状态码为 200，实际得到 %d", w.Code)
	}
}

func TestAuthMiddleware_EmptyUserNameWhenNoRequestBody(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// 创建没有请求体的 GET 请求
	req, _ := http.NewRequest("GET", "/auth/captcha", nil)
	w := httptest.NewRecorder()

	router := gin.New()
	var capturedUserName string

	router.Use(func(ctx *gin.Context) {
		requestMethod := ctx.Request.Method
		if requestMethod != "" {
			userName := "" // 模拟未登录状态

			if requestMethod != "GET" {
				var bodyBytes []byte
				if ctx.Request.Body != nil {
					bodyBytes, _ = io.ReadAll(ctx.Request.Body)
				}
				ctx.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

				if userName == "" {
					var reqParams map[string]any
					if err := json.Unmarshal(bodyBytes, &reqParams); err == nil {
						if userNameFromReq, ok := reqParams["userName"]; ok {
							if userNameStr, ok := userNameFromReq.(string); ok {
								userName = userNameStr
							}
						}
					}
				}
			}

			capturedUserName = userName
		}
		ctx.Next()
	})

	router.GET("/auth/captcha", func(ctx *gin.Context) {
		ctx.JSON(200, gin.H{"message": "captcha endpoint"})
	})

	router.ServeHTTP(w, req)

	// GET 请求不应该尝试提取用户名
	if capturedUserName != "" {
		t.Errorf("GET请求时期望用户名为空，实际得到 '%s'", capturedUserName)
	}
	if w.Code != 200 {
		t.Errorf("期望状态码为 200，实际得到 %d", w.Code)
	}
}
