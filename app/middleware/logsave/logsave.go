package logsave

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"

	"lsservice/app/dto"
	"lsservice/app/service"
	"lsservice/utils"

	"github.com/gin-gonic/gin"
)

type HandleTableAndDesc map[string]string

func getRequestBody(ctx *gin.Context) string {
	switch ctx.Request.Method {
	case http.MethodGet:
		return fmt.Sprintf("%v", ctx.Request.URL.Query())
	case http.MethodPost:
		fallthrough
	case http.MethodPut:
		fallthrough
	case http.MethodPatch:
		var bodyBytes []byte
		bodyBytes, _ = io.ReadAll(ctx.Request.Body)
		ctx.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
		return string(bodyBytes)
	}
	return ""
}

// bodyLogWriter 定义一个存储响应内容的结构体
type bodyLogWriter struct {
	gin.ResponseWriter
	bodyBuf *bytes.Buffer
}

// Write 读取响应数据
func (w bodyLogWriter) Write(b []byte) (int, error) {
	w.bodyBuf.Write(b)
	return w.ResponseWriter.Write(b)
}

// 操作记录
func Operation() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		requestMethod := ctx.Request.Method
		if requestMethod == "" {
			ctx.Next() // 注意 next()方法的作用是跳过该调用链去直接后面的中间件以及api路由
		}

		if requestMethod != "GET" {
			// 1. 获取当前用户ID
			userName := service.LogOnline.GetNameBySession(ctx)

			// 2. 获取方法名
			handlerName := strings.Split(ctx.HandlerName(), ".")

			actionTitle := strings.Trim(handlerName[1], "(*)")
			actionModule, err := service.DictData.GetValueToLog("sys_module_type", actionTitle)
			if err != nil {
				ctx.Next() // 如果映射中没有找到表关系，说明没有手动加入
			}

			operTitle := strings.Trim(handlerName[2], "-fm")
			actionType, _ := service.DictData.GetValueToLog("sys_oper_type", operTitle)

			// 3. 获取Ip, 地址
			ip := utils.GetClientIp(ctx)
			addr := utils.GetAddr(ip)

			// 4.请求参数
			requestBody := getRequestBody(ctx)

			// 如果从session获取不到用户名，尝试从请求体中获取（安全措施）
			if userName == "" && (requestMethod == "POST" || requestMethod == "PUT" || requestMethod == "PATCH") {
				var bodyBytes []byte
				if ctx.Request.Body != nil {
					bodyBytes, _ = io.ReadAll(ctx.Request.Body)
					ctx.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

					var reqParams map[string]any
					if err := json.Unmarshal(bodyBytes, &reqParams); err == nil {
						if userNameFromReq, ok := reqParams["userName"]; ok {
							if userNameStr, ok := userNameFromReq.(string); ok {
								userName = userNameStr
							}
						}
					}
				}
			}
			requestBody = strings.ReplaceAll(requestBody, " ", "")
			requestBody = strings.ReplaceAll(requestBody, "\n", "")
			requestBody = strings.ReplaceAll(requestBody, "\r", "")

			// 初始化bodyLogWriter
			blw := &bodyLogWriter{
				bodyBuf:        bytes.NewBufferString(""),
				ResponseWriter: ctx.Writer,
			}
			ctx.Writer = blw
			ctx.Next()

			// 5.响应内容
			strBody := strings.Trim(blw.bodyBuf.String(), "\n")

			go func(strBody string) {
				// 文件上传时，requestBody为空
				param := requestBody
				if ctx.Request.MultipartForm != nil {
					param = ""
				}

				opLogs := &dto.LogOperInsertReq{
					Module:        actionModule,
					BusinessType:  actionType,
					Method:        actionTitle + "-" + operTitle,
					RequestMethod: requestMethod,
					OperName:      userName,
					Url:           ctx.Request.RequestURI,
					IpAddr:        ip,
					Location:      addr,
					Param:         param,
					StatusCode:    ctx.Writer.Status(),
					JsonResult:    strBody,
					// ErrorMsg:      strBody,
					// CostTime:      0,
				}

				// fmt.Printf("%+v/n", opLogs)

				if err := service.LogOper.Insert(opLogs); err != nil {
					ctx.Next()
				}
			}(strBody)
		}
	}
}

// 访问记录
func Auth() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		requestMethod := ctx.Request.Method
		if requestMethod == "" {
			ctx.Next() // 注意 next()方法的作用是跳过该调用链去直接后面的中间件以及api路由
		}

		// 1. 获取当前用户ID
		userName := service.LogOnline.GetNameBySession(ctx)

		fmt.Println("userName", userName)

		// 2. 获取方法名
		handlerName := strings.Split(ctx.HandlerName(), ".")
		actionTitle := strings.Trim(handlerName[1], "(*)")
		if _, ok := service.DictData.GetValueToLog("sys_module_type", actionTitle); ok != nil {
			ctx.Next() // 如果映射中没有找到表关系，说明没有手动加入
		}

		operTitle := strings.Trim(handlerName[2], "-fm")
		actionType, _ := service.DictData.GetValueToLog("sys_oper_type", operTitle)

		// 3.请求参数
		var bodyBytes []byte
		if ctx.Request.Body != nil {
			bodyBytes, _ = io.ReadAll(ctx.Request.Body)
		}
		ctx.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes)) // 返回body中的原值

		// 如果从session获取不到用户名（比如登录失败时），尝试从请求体中获取
		if userName == "" {
			var reqParams map[string]any
			if err := json.Unmarshal(bodyBytes, &reqParams); err == nil {
				// 尝试从请求参数中获取用户名
				if userNameFromReq, ok := reqParams["userName"]; ok {
					if userNameStr, ok := userNameFromReq.(string); ok {
						userName = userNameStr
					}
				}
			}
		}

		// 初始化bodyLogWriter
		blw := &bodyLogWriter{
			bodyBuf:        bytes.NewBufferString(""),
			ResponseWriter: ctx.Writer,
		}
		ctx.Writer = blw
		ctx.Next()

		fmt.Println(blw.bodyBuf.String())

		// 4.响应内容
		strBody := strings.Trim(blw.bodyBuf.String(), "\n")

		go func(strBody string) {
			authLogs := &dto.LogAuthInsertReq{
				UserName:      userName,
				AuthInfoModel: *utils.GetAuthInfo(ctx),
				Status:        ctx.Writer.Status(),
				Type:          actionType,
				Msg:           strBody,
			}
			// fmt.Printf("%+v/n", authLogs)

			if err := service.LogAuth.Insert(authLogs); err != nil {
				ctx.Next()
			}
		}(strBody)
	}
}
