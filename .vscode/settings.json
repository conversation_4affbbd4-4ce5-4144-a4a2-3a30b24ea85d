{
  "gopls": {
    "ui.semanticTokens": true,
    "formatting.gofumpt": true,
  },
  "go.lintTool": "revive",
  "httpyac.responseViewContent": "exchange",
  "go.goroot": "/home/<USER>/.local/share/mise/installs/go/1.24.4",
  "debug.javascript.defaultRuntimeExecutable": {
    "pwa-node": "/home/<USER>/.local/share/mise/shims/node"
  },
  "go.alternateTools": {
    "go": "/home/<USER>/.local/share/mise/shims/go",
    "gopls": "/home/<USER>/.local/share/mise/shims/gopls"
  },
}
