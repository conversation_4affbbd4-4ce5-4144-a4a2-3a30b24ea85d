# @BASE_URL = http://127.0.0.1:8091/
@BASE_URL = http://************:8081/
# @contentType = application/x-www-form-urlencoded;charset=utf-8
# @jsessionid = JSESSIONID=da87e5a9-35a9-4bed-98be-62426ee1d6cc

# application/x-www-form-urlencoded
# multipart/form-data
# application/json
# text/xml

@contentTypeJson = application/json
@contentTypeForm = multipart/form-data

@SERVER = server

@LOGIN = auth/login
@LOGOUT = auth/logout
@CHECKLOGIN = logincheck

@USER = manage/users
@ROLE = manage/roles
@MENU = manage/menus
@DEPT = manage/depts
@POST = manage/posts
@DICT_TYPE = manage/dicttypes
@DICT_DATA = manage/dictdatas
@CONFIG = manage/configs
@NOTICE = manage/notices
@LOG_OPER = manage/logopers
@LOG_AUTH = manage/logauths
@LOG_ONLINE = manage/logonlines
@UPLOAD_IMG = manage/upload/img
@UPLOAD_FILE = manage/upload/file

@WEB_USER = website/users
@WEB_DOC = website/docs
@WEB_APP = website/apps
@WEB_ONLINE = website/onlines
@WEB_SMS = website/sms

### --- --- --- 服务器信息相关 START --- --- ---

### 1.获取服务器信息
GET {{BASE_URL}}{{SERVER}} HTTP/1.1

### --- --- --- 服务器信息相关 END --- --- ---

### --- --- --- 用户相关 START --- --- ---

### 1.登录
POST {{BASE_URL}}{{LOGIN}} HTTP/1.1
Content-Type: {{contentTypeJson}}
X-Forwarded-For: *************
User-Agent: Mozilla/5.0 (iPhone; U; CPU iPhone OS 5_1_1 like Mac OS X; en) AppleWebKit/534.46.0 (KHTML, like Gecko) CriOS/19.0.1084.60 Mobile/9B206 Safari/7534.48.3

  {
    "userName": "admin",
    "password": "123456",
    // "captcha": "111111",
    "rememberMe": true
  }

### 2.登出
POST {{BASE_URL}}{{LOGOUT}} HTTP/1.1
Content-Type: {{contentTypeJson}}

### 2-1.检查是否已登录
GET {{BASE_URL}}{{CHECKLOGIN}} HTTP/1.1

### 3.获取用户列表
GET {{BASE_URL}}{{USER}}
  ?page=1
  &limit=10
  &sortby=created_at
  &order=desc
  &userName=
  &mobile=
  &state=
  &createdAt=
  &deptId=

### 4.获取单个用户
GET {{BASE_URL}}{{USER}}/1 HTTP/1.1

### 5.新增用户
POST {{BASE_URL}}{{USER}} HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "userName": "user1",
    "nickName": "管理员1",
    "realName": "赵大大1",
    "deptId": 100,
    "mobile": "13000000001",
    "email": "<EMAIL>",
    "password": "123456",
    "gender": "1",
    "postIds": "1,2",
    "roleId": 1,
    "remark": "xxxxx"
  }

### 6.更新用户
PUT {{BASE_URL}}{{USER}}/2 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "userName": "aaaa2",
    "nickName": "姓名姓名",
    "realName": "赵大大2",
    "deptId": 103,
    "mobile": "13000000003",
    "email": "<EMAIL>",
    "gender": "1",
    "state": "1",
    "postIds": "3,4",
    "roleId": 2,
    "remark": "xx3333xx"
  }

### 7.删除用户
DELETE {{BASE_URL}}{{USER}}/2 HTTP/1.1

### 8.用户状态更改
PATCH {{BASE_URL}}{{USER}}/state/1 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "state": "0"
  }

### 9.用户密码重置
PATCH {{BASE_URL}}{{USER}}/resetpwd/1 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "password": "654321",
    "rePassword": "654321"
  }


### 10.用户密码更改
PATCH {{BASE_URL}}{{USER}}/updatepwd/1 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "oldPassword": "654321",
    "newPassword": "123456",
    "rePassword": "123456"
  }

### 9.用户名称查询
GET {{BASE_URL}}{{USER}}/checkuser
  ?userName=ss23

### 10.用户导入
POST {{BASE_URL}}{{USER}}/import HTTP/1.1
Content-Type: {{contentTypeForm}}; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="type"

file
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="users.csv"
Content-Type: text/csv

< /Downloads/users.csv
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 10.用户导出
GET {{BASE_URL}}{{USER}}/export
  ?createdAt=

### --- --- --- 用户相关 END --- --- ---

### --- --- --- 角色相关 START --- --- ---

### 1.获取角色列表
GET {{BASE_URL}}{{ROLE}}
  ?page=1
  &limit=10
  &sortby=
  &order=
  &name=
  &key=
  &state=
  &createdAt=

### 2.获取单个角色
GET {{BASE_URL}}{{ROLE}}/101 HTTP/1.1

### 3.新增角色
POST {{BASE_URL}}{{ROLE}} HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "code": "hrrr",
    "name": "人力",
    "sort": 1,
    "state": "0",
    "remark": "123",
    "menuIds": "1,2"
  }

### 4.更新角色
PUT {{BASE_URL}}{{ROLE}}/1 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "code": "admin",
    "name": "管理员",
    "sort": 1,
    "state": "0",
    "remark": "1232",
    "menuIds": "1,2"
  }

### 5.删除角色
DELETE {{BASE_URL}}{{ROLE}}/2 HTTP/1.1


### 6.角色状态更改
PATCH {{BASE_URL}}{{ROLE}}/state/1 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "state": "0"
  }

### 8.数据权限
PUT {{BASE_URL}}{{ROLE}}/data/1 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "code": "admin",
    "name": "管理员",
    "dataScope": "1",
    "deptIds": "1,2"
  }

### 9.获取角色简单列表
GET {{BASE_URL}}{{ROLE}}/simple HTTP/1.1

### 10.角色导出
GET {{BASE_URL}}{{ROLE}}/export
  ?createdAt=

### --- --- --- 角色相关 END --- --- ---

### --- --- --- 菜单相关 START --- --- ---

### 1.获取菜单树
GET {{BASE_URL}}{{MENU}}
  ?name=
  &visible=

### 2.获取单个菜单
GET {{BASE_URL}}{{MENU}}/1 HTTP/1.1

### 3.新增菜单
POST {{BASE_URL}}{{MENU}} HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "pid": 0,
    "type": "M",
    "code": "sys1",
    "name": "系统管理",
    "sort": 2,
    "visible": "1"
  }

### 4.更新菜单
PUT {{BASE_URL}}{{MENU}}/1 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "pid": 0,
    "type": "M",
    "code": "sys",
    "name": "系统管理1233356",
    "sort": 1,
    "visible": "0",
    "icon": ""
  }

### 5.删除菜单
DELETE {{BASE_URL}}{{MENU}}/2 HTTP/1.1

### 6.菜单显示更改
PATCH {{BASE_URL}}{{MENU}}/visible/1 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "visible": "1"
  }

### 7.获取菜单树
GET {{BASE_URL}}{{MENU}}/tree

### 8.根据权限获取菜单
GET {{BASE_URL}}{{MENU}}/role/100

### --- --- --- 菜单相关 END --- --- ---

### --- --- --- 部门相关 START --- --- ---

### 1.获取部门树
GET {{BASE_URL}}{{DEPT}}
  ?name=
  &state=

### 2.获取单个部门
GET {{BASE_URL}}{{DEPT}}/1 HTTP/1.1

### 3.新增部门
POST {{BASE_URL}}{{DEPT}} HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "ancestors": "0,100",
    "code": "it5",
    "name": "技术部",
    "sort": 2,
    "leader": "XXX",
    "state": ""
  }

### 4.更新部门
PUT {{BASE_URL}}{{DEPT}}/8 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "pid": 100,
    "code": "it",
    "name": "技术部2",
    "sort": 2,
    "leader": "XXX",
    "state": "1"
  }

### 5.删除部门
DELETE {{BASE_URL}}{{DEPT}}/7 HTTP/1.1

### 6.部门状态更改
PATCH {{BASE_URL}}{{DEPT}}/state/9 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "state": "1"
  }

### 7.获取部门树
GET {{BASE_URL}}{{DEPT}}/tree

### 8.部门导出
GET {{BASE_URL}}{{DEPT}}/export
  ?createdAt=

### --- --- --- 部门相关 END --- --- ---

### --- --- --- 岗位相关 START --- --- ---

### 1.获取岗位列表
GET {{BASE_URL}}{{POST}}
  ?page=1
  &limit=10
  &code=
  &name=
  &state=

### 2.获取单个岗位
GET {{BASE_URL}}{{POST}}/1 HTTP/1.1

### 3.新增岗位
POST {{BASE_URL}}{{POST}} HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "code": "ceoo",
    "name": "董事长",
    "sort": 1,
    "state": "0",
    "remark": "123"
  }

### 4.更新岗位
PUT {{BASE_URL}}{{POST}}/5 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "code": "ceoo",
    "name": "董事长234",
    "sort": 1,
    "state": "0",
    "remark": "2345"
  }

### 5.删除岗位
DELETE {{BASE_URL}}{{POST}}/2 HTTP/1.1

### 6.岗位状态更改
PATCH {{BASE_URL}}{{POST}}/state/5 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "state": "1"
  }

### 7.获取岗位简单列表
GET {{BASE_URL}}{{POST}}/simple HTTP/1.1

### 8.岗位导出
GET {{BASE_URL}}{{POST}}/export
  ?createdAt=

### --- --- --- 岗位相关 END --- --- ---

### --- --- --- 字典类型相关 START --- --- ---

### 1.获取字典类型列表
GET {{BASE_URL}}{{DICT_TYPE}}
  ?page=1
  &limit=10
  &name=
  &type=
  &state=

### 2.获取单个字典类型
GET {{BASE_URL}}{{DICT_TYPE}}/1 HTTP/1.1

### 3.新增字典类型
POST {{BASE_URL}}{{DICT_TYPE}} HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "name": "用户性别",
    "type": "sys_user_sex",
    "state": "0",
    "remark": "用户性别列表"
  }

### 4.更新字典类型
PUT {{BASE_URL}}{{DICT_TYPE}}/1 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "name": "用户性别",
    "type": "sys_user_sex",
    "state": "0",
    "remark": "用户性别列表123"
  }

### 5.删除字典类型
DELETE {{BASE_URL}}{{DICT_TYPE}}/1 HTTP/1.1

### 6.字典类型状态更改
PATCH {{BASE_URL}}{{DICT_TYPE}}/state/1 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "state": "1"
  }

### 7.字典类型导出
GET {{BASE_URL}}{{DICT_TYPE}}/export
  ?createdAt=

### --- --- --- 字典类型相关 END --- --- ---

### --- --- --- 字典数据相关 START --- --- ---

### 1.获取字典数据列表
GET {{BASE_URL}}{{DICT_DATA}}
  ?page=0
  &limit=10
  &type=sys_user_gender
  &label=
  &state=

### 2.获取单个字典数据
GET {{BASE_URL}}{{DICT_DATA}}/6 HTTP/1.1

### 3.新增字典数据
POST {{BASE_URL}}{{DICT_DATA}} HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "label": "男",
    "value": "0",
    "type": "sys_user_sex",
    "cssClass": "",
    "sort": 1,
    "listClass": "",
    "isDefault": "0",
    "state": "0",
    "remark": "性别男"
  }

### 4.更新字典数据
PUT {{BASE_URL}}{{DICT_DATA}}/1 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "label": "女",
    "value": "1",
    "type": "sys_user_sex",
    "cssClass": "",
    "sort": 1,
    "listClass": "",
    "isDefault": "0",
    "state": "0",
    "remark": "性别女"
  }

### 5.删除字典数据
DELETE {{BASE_URL}}{{DICT_DATA}}/1 HTTP/1.1

### 6.字典数据状态更改
PATCH {{BASE_URL}}{{DICT_DATA}}/state/1 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "state": "1"
  }

### 7.根据字典类型获取数据列表
GET {{BASE_URL}}{{DICT_DATA}}/lists
  ?types=sys_user_gender,sys_normal_disabled

### 8.字典数据导出
GET {{BASE_URL}}{{DICT_DATA}}/export
  ?createdAt=

### --- --- --- 字典数据相关 END --- --- ---

### --- --- --- 参数配置相关 START --- --- ---

### 1.获取参数配置列表
GET {{BASE_URL}}{{CONFIG}}
  ?page=1
  &limit=10
  &name=
  &key=
  &type=

### 2.获取单个参数配置
GET {{BASE_URL}}{{CONFIG}}/1 HTTP/1.1

### 3.新增参数配置
POST {{BASE_URL}}{{CONFIG}} HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "name": "默认密码",
    "key": "sys_user_password",
    "value": "123456",
    "type": "Y",
    "remark": "默认配置密码"
  }

### 4.更新参数配置
PUT {{BASE_URL}}{{CONFIG}}/1 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "name": "默认密码",
    "key": "sys_user_password",
    "value": "654321",
    "type": "Y",
    "remark": "默认配置密码"
  }

### 5.删除参数配置
DELETE {{BASE_URL}}{{CONFIG}}/1 HTTP/1.1

### 6.参数配置导出
GET {{BASE_URL}}{{CONFIG}}/export
  ?createdAt=

### --- --- --- 参数配置相关 END --- --- ---

### --- --- --- 通知公告相关 START --- --- ---

### 1.获取通知公告列表
GET {{BASE_URL}}{{NOTICE}}
  ?page=1
  &limit=10
  &title=
  &createdBy=
  &createdAt=

### 2.获取单个通知公告
GET {{BASE_URL}}{{NOTICE}}/LhuJJDgN_vZTu-t11W4Al HTTP/1.1

### 3.新增通知公告
POST {{BASE_URL}}{{NOTICE}} HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "title": "一个通知",
    "type": "1",
    "content": "<p>这是一个通知，大家来看一看</p><p>123</p>",
    "state": "0",
    "remark": "测试用的"
  }

### 4.更新通知公告
PUT {{BASE_URL}}{{NOTICE}}/LhuJJDgN_vZTu-t11W4Al HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "title": "一个通知-改",
    "type": "1",
    "content": "<p>这是一个通知，大家来看一看，错了</p><p>12345</p>",
    "state": "0",
    "remark": "测试用的1"
  }

### 5.删除通知公告
DELETE {{BASE_URL}}{{NOTICE}}/60c2249a-7ced-4d9c-9e8d-76fb1126f0f9 HTTP/1.1

### 6.通知公告状态更改
PATCH {{BASE_URL}}{{NOTICE}}/state/26d4acd6-8681-44c1-91c5-54581cd31157 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "state": "1"
  }

### --- --- --- 通知公告相关 END --- --- ---

### --- --- --- 在线用户相关 START --- --- ---

### 1.获取在线用户列表
GET {{BASE_URL}}{{LOG_ONLINE}}
  ?page=0
  &limit=10
  &username=
  &ipaddr=

### 2.在线用户强退
PUT {{BASE_URL}}{{LOG_ONLINE}}/offline/1 HTTP/1.1

### 4.清空离线用户
DELETE {{BASE_URL}}{{LOG_ONLINE}} HTTP/1.1

### --- --- --- 在线用户相关 END --- --- ---

### --- --- --- 登录日志相关 START --- --- ---

### 1.获取登录日志列表
GET {{BASE_URL}}{{LOG_AUTH}}
  ?page=0
  &limit=10
  &title=
  &operName=
  &type=
  &state=
  &operAt=

### 2.获取单个登录日志
GET {{BASE_URL}}{{LOG_AUTH}}/1 HTTP/1.1

### 3.删除登录日志
DELETE {{BASE_URL}}{{LOG_AUTH}}/1 HTTP/1.1

### 4.清空登录日志
DELETE {{BASE_URL}}{{LOG_AUTH}} HTTP/1.1

### 5.登录日志导出
GET {{BASE_URL}}{{LOG_AUTH}}/export
  ?createdAt=

### --- --- --- 登录日志相关 END --- --- ---

### --- --- --- 操作日志相关 START --- --- ---

### 1.获取操作日志列表
GET {{BASE_URL}}{{LOG_OPER}}
  ?page=0
  &limit=10
  &title=
  &operName=
  &type=
  &state=
  &operAt=

### 2.获取单个操作日志
GET {{BASE_URL}}{{LOG_OPER}}/1 HTTP/1.1

### 3.删除操作日志
DELETE {{BASE_URL}}{{LOG_OPER}}/1 HTTP/1.1

### 4.清空操作日志
DELETE {{BASE_URL}}{{LOG_OPER}} HTTP/1.1

### 5.操作日志导出
GET {{BASE_URL}}{{LOG_OPER}}/export
  ?createdAt=

### --- --- --- 操作日志相关 END --- --- ---

### --- --- --- 文件上传 START --- --- ---

### 图片上传
POST {{BASE_URL}}{{UPLOAD_IMG}} HTTP/1.1
Content-Type: {{contentTypeForm}}; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="type"

image
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="logo4.jpg"
Content-Type: image/jpeg

< ./logo4.jpg
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 文件上传
POST {{BASE_URL}}{{UPLOAD_FILE}} HTTP/1.1
Content-Type: {{contentTypeForm}}; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="type"

file
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="logo4.jpg"
Content-Type: image/jpeg

< ./logo4.jpg
------WebKitFormBoundary7MA4YWxkTrZu0gW--


### --- --- --- 文件上传 END --- --- ---


######### --- 网站 API --- #########

### --- --- --- 网站用户相关 START --- --- ---

### 1.获取网站用户列表
GET {{BASE_URL}}{{WEB_USER}}
  ?page=1
  &limit=10
  &username=
  &mobile=
  &state=
  &createdAt=

### 2.获取单个网站用户
GET {{BASE_URL}}{{WEB_USER}}/1 HTTP/1.1

### 3.新增网站用户
POST {{BASE_URL}}{{WEB_USER}} HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "userName": "aaa",
    "nickName": "姓名姓名",
    "mobile": "13000000001",
    "email": "<EMAIL>",
    "idcardType": "00",
    "idcardCode": "111111111111",
    "password": "111111",
    "roleId": 1,
    "remark": "xxxxx"
  }

### 4.更新网站用户
PUT {{BASE_URL}}{{WEB_USER}}/2 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "userName": "aaaa2",
    "nickName": "姓名姓名",
    "mobile": "13000000003",
    "email": "<EMAIL>",
    "gender": "1",
    "state": "1",
    "idcardType": "01",
    "idcardCode": "222222222",
    "roleId": 2,
    "remark": "xx3333xx"
  }

### 5.删除网站用户
DELETE {{BASE_URL}}{{WEB_USER}}/1 HTTP/1.1

### 6.网站用户状态更改
PATCH {{BASE_URL}}{{WEB_USER}}/state/3 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "state": "0"
  }

### 7.网站用户密码重置
PATCH {{BASE_URL}}{{WEB_USER}}/resetpwd/1 HTTP/1.1

### 8.网站用户密码更改
PATCH {{BASE_URL}}{{WEB_USER}}/updatepwd/1 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "oldPassword": "123456",
    "newPassword": "654321",
    "rePassword": "654321"
  }

### 9.网站用户名称查询
GET {{BASE_URL}}{{WEB_USER}}/checkuser
  ?username=aaaa2

### --- --- --- 网站用户相关 END --- --- ---

### --- --- --- 文档相关 START --- --- ---

### 1.获取文档列表
GET {{BASE_URL}}{{WEB_DOC}}
  ?page=1
  &limit=10
  &title=
  &type=
  &createdBy=
  &createdAt=

### 2.获取单个文档
GET {{BASE_URL}}{{WEB_DOC}}/sf4tQ6nkMjdnjDwfd8nqo HTTP/1.1

### 3.新增文档
POST {{BASE_URL}}{{WEB_DOC}} HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "title": "一个通知",
    "type": "1",
    "content": "12345",
    "html": "<p>这是一个通知，大家来看一看</p><p>123</p>",
    "state": "0",
    "remark": "测试用的"
  }

### 4.更新文档
PUT {{BASE_URL}}{{WEB_DOC}}/sf4tQ6nkMjdnjDwfd8nqo HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "title": "一个通知-改",
    "type": "1",
    "content": "djfskfjwkfjwf",
    "html": "<p>这是一个通知，大家来看一看，错了</p><p>12345</p>",
    "state": "0",
    "remark": "测试用的1"
  }

### 5.删除文档
DELETE {{BASE_URL}}{{WEB_DOC}}/b8639abd-3aec-40c2-b14d-7d3327bf34e1 HTTP/1.1

### 6.文档状态更改
PATCH {{BASE_URL}}{{WEB_DOC}}/state/eb2e0902-efd0-4a70-b202-896275c0d481 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "state": "1"
  }

### --- --- --- 文档相关 END --- --- ---

### --- --- --- 应用相关 START --- --- ---

### 1.获取应用列表
GET {{BASE_URL}}{{WEB_APP}}
  ?page=1
  &limit=10
  &title=
  &type=

### 2.获取单个应用
GET {{BASE_URL}}{{WEB_APP}}/1 HTTP/1.1

### 3.新增应用
POST {{BASE_URL}}{{WEB_APP}} HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "title": "一个文件",
    "type": "1",
    "content": "详细信息",
    "state": "0",
    "remark": "测试用的",
    "publishedAt": "2022-08-22",
    "file": "/public/storage/uploaded/files/logo4_1564440080616001536.jpg"
  }

### 4.更新应用
PUT {{BASE_URL}}{{WEB_APP}}/1 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "title": "一个文件-改",
    "type": "1",
    "content": "djfskfjwkfjwf",
    "state": "0",
    "remark": "测试用的1",
    "isTop": "0",
    "publishedAt": "2022-08-24"
  }

### 5.删除应用
DELETE {{BASE_URL}}{{WEB_APP}}/15,16 HTTP/1.1

### 6.应用状态更改
PATCH {{BASE_URL}}{{WEB_APP}}/state/2 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "state": "1"
  }

### --- --- --- 应用相关 END --- --- ---

### --- --- --- 网站在线用户相关 START --- --- ---

### 1.获取网站在线用户列表
GET {{BASE_URL}}{{WEB_ONLINE}}
  ?page=0
  &limit=10
  &username=
  &ipaddr=

### 2.网站在线用户下线
PUT {{BASE_URL}}{{WEB_ONLINE}}/offline/1 HTTP/1.1

### 4.清空网站离线用户
DELETE {{BASE_URL}}{{WEB_ONLINE}} HTTP/1.1

### --- --- --- 网站在线用户相关 END --- --- ---
