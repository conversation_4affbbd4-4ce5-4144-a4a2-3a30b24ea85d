-- 创建数据库
-- CREATE DATABASE "db_ls"
-- WITH
--   OWNER = "lsp"
-- ;

-- 创建模式
CREATE SCHEMA "s_manage";

-- ----------------------------
-- 1、用户信息表
-- ----------------------------
CREATE TYPE user_gender AS ENUM ('0', '1', '2');
-- DROP TABLE IF EXISTS "s_manage"."tb_sys_user";
CREATE TABLE IF NOT EXISTS "s_manage"."tb_sys_user" (
  "user_id" serial PRIMARY KEY,
  "dept_id" int DEFAULT 0,
  "user_name" varchar(30) NOT NULL UNIQUE,
  "nick_name" varchar(30) DEFAULT '',
  "real_name" varchar(30) DEFAULT '',
  "user_type" char(2) DEFAULT '00',
  "mobile" varchar(11) DEFAULT '',
  "email" varchar(50) DEFAULT '',
  "gender" user_gender DEFAULT '0',
  "avatar" text DEFAULT '',
  "password" varchar(50) DEFAULT '',
  "salt" varchar(20) DEFAULT '',
  "state" char(1) DEFAULT '0',
  "del_flag" int2 DEFAULT 0,
  "login_count" int DEFAULT 0,
  "login_ip" inet DEFAULT NULL,
  "login_at" timestamptz,
  "pwd_updated_at" timestamptz,
  "is_system" char(1) DEFAULT '0',
  "created_by" varchar(30) DEFAULT '',
  "created_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "updated_by" varchar(30) DEFAULT '',
  "updated_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "remark" text DEFAULT ''
);
COMMENT ON COLUMN "s_manage"."tb_sys_user"."user_id" IS '用户主键';
COMMENT ON COLUMN "s_manage"."tb_sys_user"."dept_id" IS '部门主键';
COMMENT ON COLUMN "s_manage"."tb_sys_user"."user_name" IS '用户名称';
COMMENT ON COLUMN "s_manage"."tb_sys_user"."nick_name" IS '用户昵称';
COMMENT ON COLUMN "s_manage"."tb_sys_user"."real_name" IS '真实姓名';
COMMENT ON COLUMN "s_manage"."tb_sys_user"."user_type" IS '用户类型:00系统用户,01注册用户';
COMMENT ON COLUMN "s_manage"."tb_sys_user"."mobile" IS '手机号码';
COMMENT ON COLUMN "s_manage"."tb_sys_user"."email" IS '用户邮箱';
COMMENT ON COLUMN "s_manage"."tb_sys_user"."gender" IS '用户性别:0未知,1男,2女';
COMMENT ON COLUMN "s_manage"."tb_sys_user"."avatar" IS '头像路径';
COMMENT ON COLUMN "s_manage"."tb_sys_user"."password" IS '密码';
COMMENT ON COLUMN "s_manage"."tb_sys_user"."salt" IS '盐加密';
COMMENT ON COLUMN "s_manage"."tb_sys_user"."state" IS '状态:0正常,1停用';
COMMENT ON COLUMN "s_manage"."tb_sys_user"."del_flag" IS '删除标志:0存在,2删除';
COMMENT ON COLUMN "s_manage"."tb_sys_user"."login_count" IS '登录计数';
COMMENT ON COLUMN "s_manage"."tb_sys_user"."login_ip" IS '最后登录IP';
COMMENT ON COLUMN "s_manage"."tb_sys_user"."login_at" IS '最后登录时间';
COMMENT ON COLUMN "s_manage"."tb_sys_user"."pwd_updated_at" IS '密码最后更新时间';
COMMENT ON COLUMN "s_manage"."tb_sys_user"."is_system" IS '系统内置:0否,1是';
COMMENT ON COLUMN "s_manage"."tb_sys_user"."created_by" IS '创建者';
COMMENT ON COLUMN "s_manage"."tb_sys_user"."created_at" IS '创建时间';
COMMENT ON COLUMN "s_manage"."tb_sys_user"."updated_by" IS '更新者';
COMMENT ON COLUMN "s_manage"."tb_sys_user"."updated_at" IS '更新时间';
COMMENT ON COLUMN "s_manage"."tb_sys_user"."remark" IS '备注';
COMMENT ON TABLE "s_manage"."tb_sys_user" IS '用户信息表';

-- 索引 - 优化查询性能
CREATE INDEX IF NOT EXISTS idx_sys_user_dept_id ON "s_manage"."tb_sys_user" (dept_id);
CREATE INDEX IF NOT EXISTS idx_sys_user_state ON "s_manage"."tb_sys_user" (state);
CREATE INDEX IF NOT EXISTS idx_sys_user_del_flag ON "s_manage"."tb_sys_user" (del_flag);
CREATE INDEX IF NOT EXISTS idx_sys_user_login_at ON "s_manage"."tb_sys_user" (login_at);
CREATE INDEX IF NOT EXISTS idx_sys_user_created_at ON "s_manage"."tb_sys_user" (created_at);

-- ----------------------------
-- 初始化-用户信息表数据
-- ----------------------------
INSERT INTO "s_manage"."tb_sys_user" ("dept_id", "user_name", "nick_name", "real_name", "user_type", "mobile", "email", "gender", "avatar", "password", "salt", "state", "del_flag", "login_ip", "login_at", "pwd_updated_at", "is_system", "created_by", "created_at", "updated_by", "updated_at", "remark") VALUES
(0,'admin','超级管理员','','00','','','0','','2c8e052d6dae0c4aad1515dd1ae01f51','1690359708','0','0',NULL,NULL,NULL,'1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'超级管理员');

-- ----------------------------
-- 2、角色信息表
-- ----------------------------
-- DROP TABLE IF EXISTS "s_manage"."tb_sys_role";
CREATE TABLE IF NOT EXISTS "s_manage"."tb_sys_role" (
  "role_id" serial PRIMARY KEY,
  "role_code" varchar(30) NOT NULL UNIQUE,
  "role_name" varchar(30) DEFAULT '',
  "sort" int2 DEFAULT 0,
  "data_scope" char(1) DEFAULT '0',
  "state" char(1) DEFAULT '0',
  "del_flag" int2 DEFAULT 0,
  "is_system" char(1) DEFAULT '0',
  "created_by" varchar(30) DEFAULT '',
  "created_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "updated_by" varchar(30) DEFAULT '',
  "updated_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "remark" text DEFAULT ''
);
COMMENT ON COLUMN "s_manage"."tb_sys_role"."role_id" IS '角色主键';
COMMENT ON COLUMN "s_manage"."tb_sys_role"."role_code" IS '角色编码';
COMMENT ON COLUMN "s_manage"."tb_sys_role"."role_name" IS '角色名称';
COMMENT ON COLUMN "s_manage"."tb_sys_role"."sort" IS '显示顺序';
COMMENT ON COLUMN "s_manage"."tb_sys_role"."data_scope" IS '数据范围:0全部数据权限,1自定义数据权限,2本部门数据权限,3本部门及以下数据权限';
COMMENT ON COLUMN "s_manage"."tb_sys_role"."state" IS '状态:0正常,1停用';
COMMENT ON COLUMN "s_manage"."tb_sys_role"."del_flag" IS '删除标志:0存在,2删除';
COMMENT ON COLUMN "s_manage"."tb_sys_role"."is_system" IS '系统内置:0否,1是';
COMMENT ON COLUMN "s_manage"."tb_sys_role"."created_by" IS '创建者';
COMMENT ON COLUMN "s_manage"."tb_sys_role"."created_at" IS '创建时间';
COMMENT ON COLUMN "s_manage"."tb_sys_role"."updated_by" IS '更新者';
COMMENT ON COLUMN "s_manage"."tb_sys_role"."updated_at" IS '更新时间';
COMMENT ON COLUMN "s_manage"."tb_sys_role"."remark" IS '备注';
COMMENT ON TABLE "s_manage"."tb_sys_role" IS '角色信息表';

-- 索引 - 优化查询性能
CREATE INDEX IF NOT EXISTS idx_sys_role_rolecode ON "s_manage"."tb_sys_role" (role_code);
CREATE INDEX IF NOT EXISTS idx_sys_role_state ON "s_manage"."tb_sys_role" (state);
CREATE INDEX IF NOT EXISTS idx_sys_role_del_flag ON "s_manage"."tb_sys_role" (del_flag);
-- 主键自增从100开始
SELECT setval('"s_manage"."tb_sys_role_role_id_seq"', 100, false);

-- ----------------------------
-- 初始化-角色信息表数据
-- ----------------------------
INSERT INTO "s_manage"."tb_sys_role" ("role_code", "role_name", "sort", "data_scope", "state", "del_flag", "is_system", "created_by", "created_at", "updated_by", "updated_at", "remark") VALUES
('administrator','超级管理员',1,'0','0',0,'1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'超级管理员');

-- ----------------------------
-- 3、部门信息表
-- ----------------------------
-- DROP TABLE IF EXISTS "s_manage"."tb_sys_dept";
CREATE TABLE IF NOT EXISTS "s_manage"."tb_sys_dept" (
  "dept_id" serial PRIMARY KEY,
  "dept_pid" int DEFAULT 0,
  "ancestors" varchar(50) DEFAULT '',
  "dept_code" varchar(30) NOT NULL UNIQUE,
  "dept_name" varchar(30) DEFAULT '',
  "sort" int2 DEFAULT 0,
  "leader" varchar(20) DEFAULT NULL,
  "state" char(1) DEFAULT '0',
  "del_flag" int2 DEFAULT 0,
  "is_system" char(1) DEFAULT '0',
  "created_by" varchar(30) DEFAULT '',
  "created_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "updated_by" varchar(30) DEFAULT '',
  "updated_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "remark" text DEFAULT ''
);
COMMENT ON COLUMN "s_manage"."tb_sys_dept"."dept_id" IS '部门主键';
COMMENT ON COLUMN "s_manage"."tb_sys_dept"."dept_pid" IS '父部门主键';
COMMENT ON COLUMN "s_manage"."tb_sys_dept"."ancestors" IS '祖级列表';
COMMENT ON COLUMN "s_manage"."tb_sys_dept"."dept_code" IS '部门编码';
COMMENT ON COLUMN "s_manage"."tb_sys_dept"."dept_name" IS '部门名称';
COMMENT ON COLUMN "s_manage"."tb_sys_dept"."sort" IS '显示顺序';
COMMENT ON COLUMN "s_manage"."tb_sys_dept"."leader" IS '负责人';
COMMENT ON COLUMN "s_manage"."tb_sys_dept"."state" IS '状态:0正常,1停用';
COMMENT ON COLUMN "s_manage"."tb_sys_dept"."del_flag" IS '删除标志:0存在,2删除';
COMMENT ON COLUMN "s_manage"."tb_sys_dept"."is_system" IS '系统内置:0否,1是';
COMMENT ON COLUMN "s_manage"."tb_sys_dept"."created_by" IS '创建者';
COMMENT ON COLUMN "s_manage"."tb_sys_dept"."created_at" IS '创建时间';
COMMENT ON COLUMN "s_manage"."tb_sys_dept"."updated_by" IS '更新者';
COMMENT ON COLUMN "s_manage"."tb_sys_dept"."updated_at" IS '更新时间';
COMMENT ON COLUMN "s_manage"."tb_sys_dept"."remark" IS '备注';
COMMENT ON TABLE "s_manage"."tb_sys_dept" IS '部门表';

-- 索引 - 优化查询性能
CREATE INDEX IF NOT EXISTS idx_sys_dept_pid ON "s_manage"."tb_sys_dept" (dept_pid);
CREATE INDEX IF NOT EXISTS idx_sys_dept_state ON "s_manage"."tb_sys_dept" (state);
CREATE INDEX IF NOT EXISTS idx_sys_dept_del_flag ON "s_manage"."tb_sys_dept" (del_flag);

-- ----------------------------
-- 初始化-部门信息表数据
-- ----------------------------


-- ----------------------------
-- 4、岗位信息表
-- ----------------------------
-- DROP TABLE IF EXISTS "s_manage"."tb_sys_post";
CREATE TABLE IF NOT EXISTS "s_manage"."tb_sys_post" (
  "post_id" serial PRIMARY KEY,
  "post_code" varchar(30) NOT NULL UNIQUE,
  "post_name" varchar(30) DEFAULT '',
  "sort" int2 DEFAULT 0,
  "state" char(1) DEFAULT '0',
  "is_system" char(1) DEFAULT '0',
  "created_by" varchar(30) DEFAULT '',
  "created_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "updated_by" varchar(30) DEFAULT '',
  "updated_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "remark" text DEFAULT ''
);
COMMENT ON COLUMN "s_manage"."tb_sys_post"."post_id" IS '岗位主键';
COMMENT ON COLUMN "s_manage"."tb_sys_post"."post_code" IS '岗位编码';
COMMENT ON COLUMN "s_manage"."tb_sys_post"."post_name" IS '岗位名称';
COMMENT ON COLUMN "s_manage"."tb_sys_post"."sort" IS '显示顺序';
COMMENT ON COLUMN "s_manage"."tb_sys_post"."state" IS '状态:0正常,1停用';
COMMENT ON COLUMN "s_manage"."tb_sys_post"."is_system" IS '系统内置:0否,1是';
COMMENT ON COLUMN "s_manage"."tb_sys_post"."created_by" IS '创建者';
COMMENT ON COLUMN "s_manage"."tb_sys_post"."created_at" IS '创建时间';
COMMENT ON COLUMN "s_manage"."tb_sys_post"."updated_by" IS '更新者';
COMMENT ON COLUMN "s_manage"."tb_sys_post"."updated_at" IS '更新时间';
COMMENT ON COLUMN "s_manage"."tb_sys_post"."remark" IS '备注';
COMMENT ON TABLE "s_manage"."tb_sys_post" IS '岗位信息表';

-- 索引 - 优化查询性能
CREATE INDEX IF NOT EXISTS idx_sys_post_state ON "s_manage"."tb_sys_post" (state);

-- 自增从100开始
SELECT setval('"s_manage"."tb_sys_post_post_id_seq"', 100, false);

-- ----------------------------
-- 初始化-岗位信息表数据
-- ----------------------------


-- ----------------------------
-- 5、菜单信息表
-- ----------------------------
-- DROP TABLE IF EXISTS "s_manage"."tb_sys_menu";
CREATE TABLE IF NOT EXISTS "s_manage"."tb_sys_menu" (
  "menu_id" serial PRIMARY KEY,
  "menu_pid" int DEFAULT 0,
  "menu_code" varchar(30) NOT NULL UNIQUE,
  "menu_name" varchar(30) DEFAULT '',
  "sort" int2 DEFAULT 0,
  "path" varchar(100) DEFAULT '#',
  "target" char(1) DEFAULT '0',
  "menu_type" char(1) DEFAULT '',
  "visible" char(1) DEFAULT '0',
  "is_refresh" char(1) DEFAULT '0',
  "perms" varchar(50) DEFAULT NULL,
  "icon" varchar(30) DEFAULT '',
  "is_system" char(1) DEFAULT '0',
  "created_by" varchar(30) DEFAULT '',
  "created_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "updated_by" varchar(30) DEFAULT '',
  "updated_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "remark" text DEFAULT ''
);
COMMENT ON COLUMN "s_manage"."tb_sys_menu"."menu_id" IS '菜单主键';
COMMENT ON COLUMN "s_manage"."tb_sys_menu"."menu_pid" IS '父菜单主键';
COMMENT ON COLUMN "s_manage"."tb_sys_menu"."menu_code" IS '菜单编码';
COMMENT ON COLUMN "s_manage"."tb_sys_menu"."menu_name" IS '菜单名称';
COMMENT ON COLUMN "s_manage"."tb_sys_menu"."sort" IS '显示顺序';
COMMENT ON COLUMN "s_manage"."tb_sys_menu"."path" IS '请求地址';
COMMENT ON COLUMN "s_manage"."tb_sys_menu"."target" IS '打开方式:0页签,1新窗口';
COMMENT ON COLUMN "s_manage"."tb_sys_menu"."menu_type" IS '菜单类型:M目录,C菜单,F按钮';
COMMENT ON COLUMN "s_manage"."tb_sys_menu"."visible" IS '状态:0显示,1隐藏';
COMMENT ON COLUMN "s_manage"."tb_sys_menu"."is_refresh" IS '是否刷新:0否,1是';
COMMENT ON COLUMN "s_manage"."tb_sys_menu"."perms" IS '权限标识';
COMMENT ON COLUMN "s_manage"."tb_sys_menu"."icon" IS '菜单图标';
COMMENT ON COLUMN "s_manage"."tb_sys_menu"."is_system" IS '系统内置:0否,1是';
COMMENT ON COLUMN "s_manage"."tb_sys_menu"."created_by" IS '创建者';
COMMENT ON COLUMN "s_manage"."tb_sys_menu"."created_at" IS '创建时间';
COMMENT ON COLUMN "s_manage"."tb_sys_menu"."updated_by" IS '更新者';
COMMENT ON COLUMN "s_manage"."tb_sys_menu"."updated_at" IS '更新时间';
COMMENT ON COLUMN "s_manage"."tb_sys_menu"."remark" IS '备注';
COMMENT ON TABLE "s_manage"."tb_sys_menu" IS '菜单权限表';

-- 索引 - 优化查询性能
CREATE INDEX IF NOT EXISTS idx_sys_menu_menucode ON "s_manage"."tb_sys_menu" (menu_code);
CREATE INDEX IF NOT EXISTS idx_sys_menu_pid ON "s_manage"."tb_sys_menu" (menu_pid);
CREATE INDEX IF NOT EXISTS idx_sys_menu_type ON "s_manage"."tb_sys_menu" (menu_type);
CREATE INDEX IF NOT EXISTS idx_sys_menu_visible ON "s_manage"."tb_sys_menu" (visible);

-- ----------------------------
-- 初始化-菜单信息表数据
-- ----------------------------
INSERT INTO "s_manage"."tb_sys_menu" ("menu_id", "menu_pid", "menu_code", "menu_name", "sort", "path", "target", "menu_type", "visible", "is_refresh", "perms", "icon", "is_system", "created_by", "created_at", "updated_by", "updated_at", "remark") VALUES

-- 一级菜单
(1,0,'dashboard','首页',1,'/dashboard','0','C','0','0','dashboard','DashboardOutlined','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'首页菜单'),
(2,0,'system','系统管理',2,'#','','M','0','0','system','SettingOutlined','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'系统管理目录'),
(3,0,'monitor','系统监控',3,'#','','M','0','0','monitor','VideoCameraOutlined','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'系统监控目录'),
(4,0,'website','网站管理',4,'#','','M','0','0','website','GlobalOutlined','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'网站管理目录'),


-- 二级菜单
(200,2,'user','用户管理',1,'/system/user','0','C','0','0','system:user:view','UserOutlined','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'用户管理菜单'),
(201,2,'role','角色管理',2,'/system/role','0','C','0','0','system:role:view','TeamOutlined','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'角色管理菜单'),
(202,2,'dept','部门管理',3,'/system/dept','0','C','0','0','system:dept:view','ClusterOutlined','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'部门管理菜单'),
(203,2,'post','岗位管理',4,'/system/post','0','C','0','0','system:post:view','IdcardOutlined','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'岗位管理菜单'),
(204,2,'menu','菜单管理',5,'/system/menu','0','C','0','0','system:menu:view','MenuOutlined','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'菜单管理菜单'),
(205,2,'dict','字典管理',6,'/system/dict','0','C','0','0','system:dict:view','DatabaseOutlined','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'字典管理菜单'),
(206,2,'config','参数配置',7,'/system/config','0','C','0','0','system:config:view','ContainerOutlined','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'参数配置菜单'),
(207,2,'notice','通知公告',8,'/system/notice','0','C','0','0','system:notice:view','NotificationOutlined','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'通知公告菜单'),

(300,3,'online','在线用户',1,'/monitor/online','0','C','0','0','log:online:view','UserSwitchOutlined','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'在线用户菜单'),
(301,3,'auth','登录日志',2,'/monitor/auth','0','C','0','0','log:auth:view','ContactsOutlined','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'登录日志菜单'),
(302,3,'oper','操作日志',3,'/monitor/oper','0','C','0','0','log:oper:view','CodeOutlined','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'操作日志菜单'),

(400,4,'wuser','网站用户',1,'/website/wuser','0','C','0','0','website:wuser:view','UserAddOutlined','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'网站用户菜单'),
(401,4,'wonline','在线用户',2,'/website/wonline','0','C','0','0','website:wonline:view','UserSwitchOutlined','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'网站在线用户菜单'),
(402,4,'wdoc','文档管理',3,'/website/wdoc','0','C','0','0','website:wdoc:view','FileTextOutlined','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'文档管理菜单'),
(403,4,'wapp','应用管理',4,'/website/wapp','0','C','0','0','website:wapp:view','AppstoreOutlined','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'应用管理菜单'),

-- 按钮
(2000,200,'userlist','用户列表',1,'#','','F','1','0','system:user:list','list','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,''),
(2001,200,'useradd','用户新增',2,'#','','F','0','0','system:user:add','add','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,''),
(2002,200,'useredit','用户修改',3,'#','','F','0','0','system:user:edit','edit','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,''),
(2003,200,'userremove','用户删除',4,'#','','F','0','0','system:user:remove','remove','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,''),
(2004,200,'resetpwd','密码重置',5,'#','','F','0','0','system:user:resetpwd','lock_reset','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,''),
(2005,200,'changepwd','密码修改',6,'#','','F','1','0','system:user:changepwd','published_with_changes','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'');

-- ----------------------------
-- 6、用户和角色关联表  用户N-1角色
-- ----------------------------
-- DROP TABLE IF EXISTS "s_manage"."tb_rela_user_role";
CREATE TABLE IF NOT EXISTS "s_manage"."tb_rela_user_role" (
  "user_id" int NOT NULL,
  "role_id" int NOT NULL,
  PRIMARY KEY ("user_id", "role_id")
  -- 外键约束
  -- CONSTRAINT fk_rela_user_id FOREIGN KEY (user_id) REFERENCES "s_manage"."tb_sys_user" (user_id) ON DELETE CASCADE
);
COMMENT ON COLUMN "s_manage"."tb_rela_user_role"."user_id" IS '用户主键';
COMMENT ON COLUMN "s_manage"."tb_rela_user_role"."role_id" IS '角色主键';
COMMENT ON TABLE "s_manage"."tb_rela_user_role" IS '用户和角色关联表: 用户N-1角色';

-- 索引 - 优化查询性能
CREATE INDEX IF NOT EXISTS idx_rela_user_role_user_id ON "s_manage"."tb_rela_user_role" (user_id);
CREATE INDEX IF NOT EXISTS idx_rela_user_role_role_id ON "s_manage"."tb_rela_user_role" (role_id);

-- ----------------------------
-- 初始化-用户和角色关联表数据
-- ----------------------------
INSERT INTO "s_manage"."tb_rela_user_role" ("user_id", "role_id") VALUES
(1,100);

-- ----------------------------
-- 7、用户与岗位关联表: 用户1-N岗位
-- ----------------------------
-- DROP TABLE IF EXISTS "s_manage"."tb_rela_user_post";
CREATE TABLE IF NOT EXISTS "s_manage"."tb_rela_user_post" (
  "user_id" int NOT NULL,
  "post_id" int NOT NULL,
  PRIMARY KEY ("user_id", "post_id")
  -- 外键约束
  -- CONSTRAINT fk_rela_user_id FOREIGN KEY (user_id) REFERENCES "s_manage"."tb_sys_user" (user_id) ON DELETE CASCADE
);
COMMENT ON COLUMN "s_manage"."tb_rela_user_post"."user_id" IS '用户主键';
COMMENT ON COLUMN "s_manage"."tb_rela_user_post"."post_id" IS '岗位主键';
COMMENT ON TABLE "s_manage"."tb_rela_user_post" IS '用户与岗位关联表: 用户1-N岗位';

-- 索引 - 优化查询性能
CREATE INDEX IF NOT EXISTS idx_rela_user_post_user_id ON "s_manage"."tb_rela_user_post" (user_id);
CREATE INDEX IF NOT EXISTS idx_rela_user_post_post_id ON "s_manage"."tb_rela_user_post" (post_id);

-- ----------------------------
-- 初始化-用户与岗位关联表数据
-- ----------------------------
INSERT INTO "s_manage"."tb_rela_user_post" ("user_id", "post_id") VALUES
(1,100);

-- ----------------------------
-- 8、角色和菜单关联表  角色1-N菜单
-- ----------------------------
-- DROP TABLE IF EXISTS "s_manage"."tb_rela_role_menu";
CREATE TABLE IF NOT EXISTS "s_manage"."tb_rela_role_menu" (
  "role_id" int NOT NULL,
  "menu_id" int NOT NULL,
  PRIMARY KEY ("role_id", "menu_id")
  -- 外键约束
  -- CONSTRAINT fk_rela_role_id FOREIGN KEY (role_id) REFERENCES "s_manage"."tb_sys_role" (role_id) ON DELETE CASCADE
);
COMMENT ON COLUMN "s_manage"."tb_rela_role_menu"."role_id" IS '角色主键';
COMMENT ON COLUMN "s_manage"."tb_rela_role_menu"."menu_id" IS '菜单主键';
COMMENT ON TABLE "s_manage"."tb_rela_role_menu" IS '角色和菜单关联表: 角色1-N菜单';

-- 索引 - 优化查询性能
CREATE INDEX IF NOT EXISTS idx_rela_role_menu_role_id ON "s_manage"."tb_rela_role_menu" (role_id);
CREATE INDEX IF NOT EXISTS idx_rela_role_menu_menu_id ON "s_manage"."tb_rela_role_menu" (menu_id);

-- ----------------------------
-- 初始化-角色和菜单关联表数据
-- ----------------------------
INSERT INTO "s_manage"."tb_rela_role_menu" ("role_id", "menu_id") VALUES
(100,1), (100,2), (100,3), (100,4), (100,200), (100,201), (100,202), (100,203), (100,204), (100,205), (100,206), (100,207), (100,300), (100,301), (100,302), (100,400), (100,401), (100,402), (100,403);

-- ----------------------------
-- 9、角色和部门关联表: 角色1-N部门
-- ----------------------------
-- DROP TABLE IF EXISTS "s_manage"."tb_rela_role_dept";
CREATE TABLE IF NOT EXISTS "s_manage"."tb_rela_role_dept" (
  "role_id" int NOT NULL,
  "dept_id" int NOT NULL,
  PRIMARY KEY ("role_id", "dept_id")
  -- 外键约束
  -- CONSTRAINT fk_rela_role_id FOREIGN KEY (role_id) REFERENCES "s_manage"."tb_sys_role" (role_id) ON DELETE CASCADE
);
COMMENT ON COLUMN "s_manage"."tb_rela_role_dept"."role_id" IS '角色主键';
COMMENT ON COLUMN "s_manage"."tb_rela_role_dept"."dept_id" IS '部门主键';
COMMENT ON TABLE "s_manage"."tb_rela_role_dept" IS '角色和部门关联表: 角色1-N部门';

-- 索引 - 优化查询性能
CREATE INDEX IF NOT EXISTS idx_rela_role_dept_role_id ON "s_manage"."tb_rela_role_dept" (role_id);
CREATE INDEX IF NOT EXISTS idx_rela_role_dept_dept_id ON "s_manage"."tb_rela_role_dept" (dept_id);

-- ----------------------------
-- 初始化-角色和部门关联表数据
-- ----------------------------


-- ----------------------------
-- 10、字典类型表
-- ----------------------------
-- DROP TABLE IF EXISTS "s_manage"."tb_dict_type";
CREATE TABLE IF NOT EXISTS "s_manage"."tb_dict_type" (
  "dict_id" serial PRIMARY KEY,
  "dict_name" varchar(30) DEFAULT '',
  "dict_type" varchar(50) NOT NULL UNIQUE,
  "state" char(1) DEFAULT '0',
  "is_system" char(1) DEFAULT '0',
  "created_by" varchar(30) DEFAULT '',
  "created_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "updated_by" varchar(30) DEFAULT '',
  "updated_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "remark" text DEFAULT ''
);
COMMENT ON COLUMN "s_manage"."tb_dict_type"."dict_id" IS '字典主键';
COMMENT ON COLUMN "s_manage"."tb_dict_type"."dict_name" IS '字典名称';
COMMENT ON COLUMN "s_manage"."tb_dict_type"."dict_type" IS '字典类型';
COMMENT ON COLUMN "s_manage"."tb_dict_type"."state" IS '状态:0正常,1停用';
COMMENT ON COLUMN "s_manage"."tb_dict_type"."is_system" IS '系统内置:0否,1是';
COMMENT ON COLUMN "s_manage"."tb_dict_type"."created_by" IS '创建者';
COMMENT ON COLUMN "s_manage"."tb_dict_type"."created_at" IS '创建时间';
COMMENT ON COLUMN "s_manage"."tb_dict_type"."updated_by" IS '更新者';
COMMENT ON COLUMN "s_manage"."tb_dict_type"."updated_at" IS '更新时间';
COMMENT ON COLUMN "s_manage"."tb_dict_type"."remark" IS '备注';
COMMENT ON TABLE "s_manage"."tb_dict_type" IS '字典类型表';

-- 索引 - 优化查询性能
CREATE INDEX IF NOT EXISTS idx_dict_type_state ON "s_manage"."tb_dict_type" (state);

-- ----------------------------
-- 初始化-字典类型表数据
-- ----------------------------
INSERT INTO "s_manage"."tb_dict_type" ("dict_name", "dict_type", "state", "is_system", "created_by", "created_at", "updated_by", "updated_at", "remark") VALUES

('系统开关','sys_normal_disabled','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,''),
('系统是否','sys_yes_no','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,''),
('用户性别','sys_user_gender','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,''),
('菜单状态','sys_menu_visible','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,''),
('菜单类型','sys_menu_type','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,''),
('数据范围','sys_data_scope','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,''),
('通知类型','sys_notice_type','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,''),
('通知状态','sys_notice_state','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,''),
('会话状态','sys_online_state','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,''),
('系统模块','sys_module_type','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,''),
('操作类型','sys_oper_type','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,''),
('操作状态','sys_oper_state','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,''),
('文档类型','web_doc_type','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,''),
('应用类型','web_app_type','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'');

-- ----------------------------
-- 11、字典数据表
-- ----------------------------
-- DROP TABLE IF EXISTS "s_manage"."tb_dict_data";
CREATE TABLE IF NOT EXISTS "s_manage"."tb_dict_data" (
  "dict_code" serial PRIMARY KEY,
  "dict_label" varchar(30) DEFAULT '',
  "dict_value" varchar(2) NOT NULL,
  "dict_type" varchar(50) DEFAULT '',
  "sort" int2 DEFAULT 0,
  "css_class" varchar(50) DEFAULT NULL,
  "list_class" varchar(50) DEFAULT NULL,
  "is_default" char(1) DEFAULT '0',
  "state" char(1) DEFAULT '0',
  "is_system" char(1) DEFAULT '0',
  "created_by" varchar(30) DEFAULT '',
  "created_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "updated_by" varchar(30) DEFAULT '',
  "updated_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "remark" text DEFAULT ''
);
COMMENT ON COLUMN "s_manage"."tb_dict_data"."dict_code" IS '字典编码';
COMMENT ON COLUMN "s_manage"."tb_dict_data"."dict_label" IS '字典标签';
COMMENT ON COLUMN "s_manage"."tb_dict_data"."dict_value" IS '字典键值';
COMMENT ON COLUMN "s_manage"."tb_dict_data"."dict_type" IS '字典类型';
COMMENT ON COLUMN "s_manage"."tb_dict_data"."sort" IS '字典排序';
COMMENT ON COLUMN "s_manage"."tb_dict_data"."css_class" IS '样式属性(其他样式扩展)';
COMMENT ON COLUMN "s_manage"."tb_dict_data"."list_class" IS '表格回显样式';
COMMENT ON COLUMN "s_manage"."tb_dict_data"."is_default" IS '是否默认:0否,1是';
COMMENT ON COLUMN "s_manage"."tb_dict_data"."state" IS '状态:0正常,1停用';
COMMENT ON COLUMN "s_manage"."tb_dict_data"."is_system" IS '系统内置:0否,1是';
COMMENT ON COLUMN "s_manage"."tb_dict_data"."created_by" IS '创建者';
COMMENT ON COLUMN "s_manage"."tb_dict_data"."created_at" IS '创建时间';
COMMENT ON COLUMN "s_manage"."tb_dict_data"."updated_by" IS '更新者';
COMMENT ON COLUMN "s_manage"."tb_dict_data"."updated_at" IS '更新时间';
COMMENT ON COLUMN "s_manage"."tb_dict_data"."remark" IS '备注';
COMMENT ON TABLE "s_manage"."tb_dict_data" IS '字典数据表';

-- 索引 - 优化查询性能
CREATE INDEX IF NOT EXISTS idx_dict_data_type ON "s_manage"."tb_dict_data" (dict_type);
CREATE INDEX IF NOT EXISTS idx_dict_data_state ON "s_manage"."tb_dict_data" (state);
CREATE INDEX IF NOT EXISTS idx_dict_data_sort ON "s_manage"."tb_dict_data" (sort);

-- ----------------------------
-- 初始化-字典数据表数据
-- ----------------------------
INSERT INTO "s_manage"."tb_dict_data" ("dict_label", "dict_value", "dict_type", "sort", "css_class", "list_class", "is_default", "state", "is_system", "created_by", "created_at", "updated_by", "updated_at", "remark") VALUES

-- 系统开关
('正常','0','sys_normal_disabled',1,'','success','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,''),
('停用','1','sys_normal_disabled',2,'','error','1','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,''),
-- 系统是否
('否','0','sys_yes_no',1,'','error','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'系统默认否'),
('是','1','sys_yes_no',2,'','success','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'系统默认是'),
-- 用户性别
('未知','0','sys_user_gender',1,'secondary','','1','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'不便透露'),
('男','1','sys_user_gender',2,'success','','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'性别男'),
('女','2','sys_user_gender',3,'danger','','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'性别女'),
-- 菜单状态
('显示','0','sys_menu_visible',1,'','success','1','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'菜单显示'),
('隐藏','1','sys_menu_visible',2,'','error','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'菜单隐藏'),
-- 菜单类型
('目录','M','sys_menu_type',1,'','blue','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,''),
('菜单','C','sys_menu_type',2,'','green','1','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,''),
('按钮','F','sys_menu_type',3,'','orange','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,''),
-- 数据范围
('全部数据权限','0','sys_data_scope',1,'','','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,''),
('自定义数据权限','1','sys_data_scope',2,'','','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,''),
('本部门数据权限','2','sys_data_scope',3,'','','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,''),
('本部门及以下数据权限','3','sys_data_scope',4,'','','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,''),
-- 通知类型
('通知','0','sys_notice_type',1,'','orange','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,''),
('公告','1','sys_notice_type',2,'','blue','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,''),
-- 通知状态
('发布','0','sys_notice_state',1,'','green','1','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'发布状态'),
('下线','1','sys_notice_state',2,'','red','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'下线状态'),
('草稿','2','sys_notice_state',3,'','gold','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'草稿状态'),
-- 会话状态
('在线','0','sys_online_state',1,'','green','1','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'用户在线'),
('离线','1','sys_online_state',2,'','red','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'用户已离线'),
('超时','2','sys_online_state',3,'','orange','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'用户超时'),
-- 系统模块
('用户管理','00','sys_module_type',1,'','','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'userCtl'),
('角色管理','01','sys_module_type',2,'','','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'roleCtl'),
('菜单管理','02','sys_module_type',3,'','','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'menuCtl'),
('部门管理','03','sys_module_type',4,'','','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'deptCtl'),
('岗位管理','04','sys_module_type',5,'','','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'postCtl'),
('字典类型','05','sys_module_type',6,'','','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'dictTypeCtl'),
('字典数据','06','sys_module_type',7,'','','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'dictDataCtl'),
('参数管理','07','sys_module_type',8,'','','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'configCtl'),
('通知公告','08','sys_module_type',9,'','','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'noticeCtl'),
('在线用户','09','sys_module_type',10,'','','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'logOnlineCtl'),
('操作日志','10','sys_module_type',11,'','','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'logOperCtl'),
('登录日志','11','sys_module_type',12,'','','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'logAuthCtl'),
('上传操作','12','sys_module_type',13,'','','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'uploadCtl'),
('网站用户','13','sys_module_type',14,'','','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'webUserCtl'),
('网站在线用户','14','sys_module_type',15,'','','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'webOnlineCtl'),
('文档管理','15','sys_module_type',16,'','','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'webDocCtl'),
('应用管理','16','sys_module_type',17,'','','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'webAppCtl'),
-- 操作类型
('其他','00','sys_oper_type',1,'','','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'*'),
('新增','01','sys_oper_type',2,'','blue','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'Insert'),
('修改','02','sys_oper_type',3,'','processing','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'Update'),
('删除','03','sys_oper_type',4,'','red','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'Delete'),
('状态更改','04','sys_oper_type',5,'','pink','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'State'),
('清空','05','sys_oper_type',6,'','orange','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'Empty'),
('导入','06','sys_oper_type',7,'','success','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'Import'),
('导出','07','sys_oper_type',8,'','error','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'Export'),
('强退','08','sys_oper_type',9,'','pink','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'ForceLogout'),
('密码重置','09','sys_oper_type',10,'','cyan','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'ResetPwd'),
('密码修改','10','sys_oper_type',11,'','cyan','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'UpdatePwd'),
('设置权限','11','sys_oper_type',12,'','lime','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'UpdateAuth'),
('解锁','12','sys_oper_type',13,'','volcano','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'Unlock'),
('查询','13','sys_oper_type',14,'','blue','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'Query'),
('注销','98','sys_oper_type',99,'','orange','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'Logout'),
('登录','99','sys_oper_type',100,'','blue','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'Login'),
-- 操作状态
('成功','0','sys_oper_state',1,'','success','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'Success'),
('失败','1','sys_oper_state',2,'','error','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,''),
-- 文档类型
('客户端','0','web_doc_type',1,'','blue','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,''),
('服务端','1','web_doc_type',2,'','orange','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,''),
-- 应用类型
('生产版本','0','web_app_type',1,'','blue','1','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,''),
('测试版本','1','web_app_type',2,'','orange','0','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'');


-- ----------------------------
-- 12、在线用户记录
-- ----------------------------
-- DROP TABLE IF EXISTS "s_manage"."tb_log_online";
CREATE TABLE IF NOT EXISTS "s_manage"."tb_log_online" (
  "session_id" varchar(21) NOT NULL PRIMARY KEY,
  "user_name" varchar(30) DEFAULT '',
  "status" char(1) DEFAULT '',
  "ip_addr" inet DEFAULT NULL,
  "location" varchar(255) DEFAULT '',
  "browser" varchar(50) DEFAULT '',
  "browser_version" varchar(15) DEFAULT '',
  "os" varchar(50) DEFAULT '',
  "os_version" varchar(15) DEFAULT '',
  "device" varchar(15) DEFAULT '',
  "start_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "last_access_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "expire_time" int DEFAULT 30
);
COMMENT ON COLUMN "s_manage"."tb_log_online"."session_id" IS '会话主键';
COMMENT ON COLUMN "s_manage"."tb_log_online"."user_name" IS '登录账号';
COMMENT ON COLUMN "s_manage"."tb_log_online"."status" IS '状态:0(online)在线,1(offline)离线';
COMMENT ON COLUMN "s_manage"."tb_log_online"."ip_addr" IS '登录IP地址';
COMMENT ON COLUMN "s_manage"."tb_log_online"."location" IS '登录地点';
COMMENT ON COLUMN "s_manage"."tb_log_online"."browser" IS '浏览器类型';
COMMENT ON COLUMN "s_manage"."tb_log_online"."browser_version" IS '浏览器版本';
COMMENT ON COLUMN "s_manage"."tb_log_online"."os" IS '操作系统';
COMMENT ON COLUMN "s_manage"."tb_log_online"."os_version" IS '操作系统版本';
COMMENT ON COLUMN "s_manage"."tb_log_online"."device" IS '设备类型';
COMMENT ON COLUMN "s_manage"."tb_log_online"."start_at" IS '创建时间';
COMMENT ON COLUMN "s_manage"."tb_log_online"."last_access_at" IS '最后访问时间';
COMMENT ON COLUMN "s_manage"."tb_log_online"."expire_time" IS '超时时间,单位为分钟';
COMMENT ON TABLE "s_manage"."tb_log_online" IS '在线用户记录';

-- 索引 - 优化查询性能
CREATE INDEX IF NOT EXISTS idx_log_online_user_name ON "s_manage"."tb_log_online" (user_name);
CREATE INDEX IF NOT EXISTS idx_log_online_status ON "s_manage"."tb_log_online" (status);
CREATE INDEX IF NOT EXISTS idx_log_online_start_at ON "s_manage"."tb_log_online" (start_at);
CREATE INDEX IF NOT EXISTS idx_log_online_last_access_at ON "s_manage"."tb_log_online" (last_access_at);


-- ----------------------------
-- 13、系统访问记录
-- ----------------------------
-- DROP TABLE IF EXISTS "s_manage"."tb_log_auth";
CREATE TABLE IF NOT EXISTS "s_manage"."tb_log_auth" (
  "auth_id" serial PRIMARY KEY,
  "user_name" varchar(30) DEFAULT '',
  "ip_addr" inet DEFAULT NULL,
  "location" varchar(100) DEFAULT '',
  "browser" varchar(50) DEFAULT '',
  "browser_version" varchar(15) DEFAULT '',
  "os" varchar(50) DEFAULT '',
  "os_version" varchar(15) DEFAULT '',
  "device" varchar(15) DEFAULT '',
  "status" char(1) DEFAULT '',
  "type" char(2) DEFAULT '',
  "msg" text DEFAULT '',
  "auth_at" timestamptz DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON COLUMN "s_manage"."tb_log_auth"."auth_id" IS '访问主键';
COMMENT ON COLUMN "s_manage"."tb_log_auth"."user_name" IS '登录账号';
COMMENT ON COLUMN "s_manage"."tb_log_auth"."ip_addr" IS '访问IP地址';
COMMENT ON COLUMN "s_manage"."tb_log_auth"."location" IS '访问地点';
COMMENT ON COLUMN "s_manage"."tb_log_auth"."browser" IS '浏览器类型';
COMMENT ON COLUMN "s_manage"."tb_log_auth"."browser_version" IS '浏览器版本';
COMMENT ON COLUMN "s_manage"."tb_log_auth"."os" IS '操作系统';
COMMENT ON COLUMN "s_manage"."tb_log_auth"."os_version" IS '操作系统版本';
COMMENT ON COLUMN "s_manage"."tb_log_auth"."device" IS '设备类型';
COMMENT ON COLUMN "s_manage"."tb_log_auth"."status" IS '状态:0成功,1失败';
COMMENT ON COLUMN "s_manage"."tb_log_auth"."type" IS '类型:99登录,98注销';
COMMENT ON COLUMN "s_manage"."tb_log_auth"."msg" IS '提示消息';
COMMENT ON COLUMN "s_manage"."tb_log_auth"."auth_at" IS '访问时间';
COMMENT ON TABLE "s_manage"."tb_log_auth" IS '系统访问记录';

-- 索引 - 优化查询性能
CREATE INDEX IF NOT EXISTS idx_log_auth_user_name ON "s_manage"."tb_log_auth" (user_name);
CREATE INDEX IF NOT EXISTS idx_log_auth_status ON "s_manage"."tb_log_auth" (status);
CREATE INDEX IF NOT EXISTS idx_log_auth_type ON "s_manage"."tb_log_auth" (type);
CREATE INDEX IF NOT EXISTS idx_log_auth_auth_at ON "s_manage"."tb_log_auth" (auth_at);


-- ----------------------------
-- 14、操作日志记录
-- ----------------------------
-- DROP TABLE IF EXISTS "s_manage"."tb_log_oper";
CREATE TABLE IF NOT EXISTS "s_manage"."tb_log_oper" (
  "oper_id" serial,
  "module" char(2) DEFAULT '',
  "business_type" char(2) DEFAULT '00',
  "method" varchar(100) DEFAULT '',
  "request_method" varchar(10) DEFAULT '',
  "operator_type" char(1) DEFAULT '0',
  "oper_name" varchar(30) DEFAULT '',
  "url" varchar(255) DEFAULT '',
  "ip_addr" inet DEFAULT NULL,
  "location" varchar(100) DEFAULT '',
  "param" jsonb DEFAULT '{}',
  "status_code" int2 DEFAULT 200,
  "json_result" jsonb DEFAULT '{}',
  "error_msg" text DEFAULT '',
  "oper_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "cost_time" int DEFAULT 0,
  PRIMARY KEY ("oper_id", "oper_at")
) PARTITION BY RANGE (oper_at);
COMMENT ON COLUMN "s_manage"."tb_log_oper"."oper_id" IS '日志主键';
COMMENT ON COLUMN "s_manage"."tb_log_oper"."module" IS '系统模块:00用户管理,01角色管理,02菜单管理,03部门管理,04岗位管理,05字典类型,06字典数据,07参数管理,08通知公告,09在线用户,10操作日志,11登录日志,12上传操作,13网站用户,14网站在线用户,15文档管理,16应用管理';
COMMENT ON COLUMN "s_manage"."tb_log_oper"."business_type" IS '业务类型:00其他,01新增,02修改,03删除,04状态更改,05清空,06导入,07导出,08强退,09密码重置,10密码修改,11设置权限,12解锁,13查询';
COMMENT ON COLUMN "s_manage"."tb_log_oper"."method" IS '方法名称';
COMMENT ON COLUMN "s_manage"."tb_log_oper"."request_method" IS '请求方式';
COMMENT ON COLUMN "s_manage"."tb_log_oper"."operator_type" IS '操作人员类别:0其它,1后台用户,2手机端用户';
COMMENT ON COLUMN "s_manage"."tb_log_oper"."oper_name" IS '操作人员';
COMMENT ON COLUMN "s_manage"."tb_log_oper"."url" IS '请求URL';
COMMENT ON COLUMN "s_manage"."tb_log_oper"."ip_addr" IS '主机地址';
COMMENT ON COLUMN "s_manage"."tb_log_oper"."location" IS '操作地点';
COMMENT ON COLUMN "s_manage"."tb_log_oper"."param" IS '请求参数';
COMMENT ON COLUMN "s_manage"."tb_log_oper"."status_code" IS '返回状态:200正常,500异常';
COMMENT ON COLUMN "s_manage"."tb_log_oper"."json_result" IS '返回参数';
COMMENT ON COLUMN "s_manage"."tb_log_oper"."error_msg" IS '错误消息';
COMMENT ON COLUMN "s_manage"."tb_log_oper"."oper_at" IS '操作时间';
COMMENT ON COLUMN "s_manage"."tb_log_oper"."cost_time" IS '消耗时间';
COMMENT ON TABLE "s_manage"."tb_log_oper" IS '操作日志记录(按月分区)';

-- 分区(按月)
CREATE TABLE "s_manage"."tb_log_oper_y2024m08" PARTITION OF "s_manage"."tb_log_oper"
    FOR VALUES FROM ('2024-08-01') TO ('2024-09-01');
    -- 2024年8月

CREATE TABLE "s_manage"."tb_log_oper_y2024m09" PARTITION OF "s_manage"."tb_log_oper"
    FOR VALUES FROM ('2024-09-01') TO ('2024-10-01');
    -- 2024年9月

CREATE TABLE "s_manage"."tb_log_oper_y2024m10" PARTITION OF "s_manage"."tb_log_oper"
    FOR VALUES FROM ('2024-10-01') TO ('2024-11-01'); -- 2024年10月

CREATE TABLE "s_manage"."tb_log_oper_y2024m11" PARTITION OF "s_manage"."tb_log_oper"
    FOR VALUES FROM ('2024-11-01') TO ('2024-12-01'); -- 2024年11月

CREATE TABLE "s_manage"."tb_log_oper_y2024m12" PARTITION OF "s_manage"."tb_log_oper"
    FOR VALUES FROM ('2024-12-01') TO ('2025-01-01'); -- 2024年12月

CREATE TABLE "s_manage"."tb_log_oper_future" PARTITION OF "s_manage"."tb_log_oper"
    FOR VALUES FROM ('2025-01-01') TO (MAXVALUE);

-- 索引 - 优化查询性能 (在主表上创建索引会自动应用到所有分区)
CREATE INDEX IF NOT EXISTS idx_log_oper_module ON "s_manage"."tb_log_oper" (module);
CREATE INDEX IF NOT EXISTS idx_log_oper_business_type ON "s_manage"."tb_log_oper" (business_type);
CREATE INDEX IF NOT EXISTS idx_log_oper_oper_name ON "s_manage"."tb_log_oper" (oper_name);
CREATE INDEX IF NOT EXISTS idx_log_oper_status_code ON "s_manage"."tb_log_oper" (status_code);
CREATE INDEX IF NOT EXISTS idx_log_oper_oper_at ON "s_manage"."tb_log_oper" (oper_at);


-- ----------------------------
-- 15、参数配置表
-- ----------------------------
-- DROP TABLE IF EXISTS "s_manage"."tb_other_config";
CREATE TABLE IF NOT EXISTS "s_manage"."tb_other_config" (
  "config_id" serial PRIMARY KEY,
  "config_key" varchar(100) NOT NULL UNIQUE,
  "config_name" varchar(100) DEFAULT '',
  "config_value" varchar(500) NOT NULL,
  "is_system" char(1) DEFAULT '0',
  "created_by" varchar(30) DEFAULT '',
  "created_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "updated_by" varchar(30) DEFAULT '',
  "updated_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "remark" text DEFAULT ''
);
COMMENT ON COLUMN "s_manage"."tb_other_config"."config_id" IS '参数主键';
COMMENT ON COLUMN "s_manage"."tb_other_config"."config_key" IS '参数键名';
COMMENT ON COLUMN "s_manage"."tb_other_config"."config_name" IS '参数名称';
COMMENT ON COLUMN "s_manage"."tb_other_config"."config_value" IS '参数键值';
COMMENT ON COLUMN "s_manage"."tb_other_config"."is_system" IS '系统内置:0否,1是';
COMMENT ON COLUMN "s_manage"."tb_other_config"."created_by" IS '创建者';
COMMENT ON COLUMN "s_manage"."tb_other_config"."created_at" IS '创建时间';
COMMENT ON COLUMN "s_manage"."tb_other_config"."updated_by" IS '更新者';
COMMENT ON COLUMN "s_manage"."tb_other_config"."updated_at" IS '更新时间';
COMMENT ON COLUMN "s_manage"."tb_other_config"."remark" IS '备注';
COMMENT ON TABLE "s_manage"."tb_other_config" IS '参数配置表';

-- 索引 - 优化查询性能
CREATE INDEX IF NOT EXISTS idx_other_config_key ON "s_manage"."tb_other_config" (config_key);

-- ----------------------------
-- 初始化-参数配置表数据
-- ----------------------------
INSERT INTO "s_manage"."tb_other_config" ("config_key", "config_name", "config_value", "is_system", "created_by", "created_at", "updated_by", "updated_at", "remark") VALUES
('sys.user.initPassword','用户管理-账号初始密码','123456','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'初始化密码:123456'),
('sys.account.chrtype','用户管理-密码字符范围','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'默认任意字符范围:0任意(密码可以输入任意字符),1数字(密码只能为0-9数字),2英文字母(密码只能为a-z和A-Z字母),3字母和数字(密码必须包含字母、数字),4字母数字和特殊字符(目前支持的特殊字符包括：~!@#$%^&*()-=_+)'),
('sys.account.initPasswordModify','用户管理-初始密码修改策略','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'0初始密码修改策略关闭,没有任何提示,1提醒用户,如果未修改初始密码,则在登录时就会提醒修改密码对话框'),
('sys.account.passwordValidateDays','用户管理-账号密码更新周期','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'密码更新周期(填写数字,数据初始化值为0不限制,若修改必须为大于0小于365的正整数),如果超过这个周期登录系统时,则在登录时就会提醒修改密码对话框'),
('sys.login.blackIPList','用户登录-黑名单列表','','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'设置登录IP黑名单限制,多个匹配项以;分隔,支持匹配(*通配、网段)'),
('sys.notice.state','通知公告-状态','0','1','',CURRENT_TIMESTAMP,'',CURRENT_TIMESTAMP,'0正常,1关闭');


-- ----------------------------
-- 16、通知公告表
-- ----------------------------
-- DROP TABLE IF EXISTS "s_manage"."tb_other_notice";
CREATE TABLE IF NOT EXISTS "s_manage"."tb_other_notice" (
  "notice_id" varchar(21) NOT NULL PRIMARY KEY,
  "notice_title" varchar(50) NOT NULL,
  "notice_type" char(1) NOT NULL,
  "path" text DEFAULT '',
  "state" char(1) DEFAULT '0',
  "is_top" char(1) DEFAULT '0',
  "browse" int DEFAULT 0,
  "created_by" varchar(30) DEFAULT '',
  "created_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "updated_by" varchar(30) DEFAULT '',
  "updated_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "remark" text DEFAULT ''
);
COMMENT ON COLUMN "s_manage"."tb_other_notice"."notice_id" IS '公告主键';
COMMENT ON COLUMN "s_manage"."tb_other_notice"."notice_title" IS '公告标题';
COMMENT ON COLUMN "s_manage"."tb_other_notice"."notice_type" IS '公告类型:1通知,2公告';
COMMENT ON COLUMN "s_manage"."tb_other_notice"."path" IS '公告存放地址';
COMMENT ON COLUMN "s_manage"."tb_other_notice"."state" IS '状态:0正常,1关闭';
COMMENT ON COLUMN "s_manage"."tb_other_notice"."is_top" IS '是否置顶:0否,1是';
COMMENT ON COLUMN "s_manage"."tb_other_notice"."browse" IS '阅读量';
COMMENT ON COLUMN "s_manage"."tb_other_notice"."created_by" IS '创建者';
COMMENT ON COLUMN "s_manage"."tb_other_notice"."created_at" IS '创建时间';
COMMENT ON COLUMN "s_manage"."tb_other_notice"."updated_by" IS '更新者';
COMMENT ON COLUMN "s_manage"."tb_other_notice"."updated_at" IS '更新时间';
COMMENT ON COLUMN "s_manage"."tb_other_notice"."remark" IS '备注';
COMMENT ON TABLE "s_manage"."tb_other_notice" IS '通知公告表';

-- 索引 - 优化查询性能
CREATE INDEX IF NOT EXISTS idx_other_notice_type ON "s_manage"."tb_other_notice" (notice_type);
CREATE INDEX IF NOT EXISTS idx_other_notice_state ON "s_manage"."tb_other_notice" (state);
CREATE INDEX IF NOT EXISTS idx_other_notice_is_top ON "s_manage"."tb_other_notice" (is_top);
CREATE INDEX IF NOT EXISTS idx_other_notice_created_at ON "s_manage"."tb_other_notice" (created_at);

-- ----------------------------
-- 数据库优化设置
-- ----------------------------

-- 启用自动更新统计信息
ALTER TABLE "s_manage"."tb_sys_user" SET (autovacuum_enabled = true);
ALTER TABLE "s_manage"."tb_sys_role" SET (autovacuum_enabled = true);
ALTER TABLE "s_manage"."tb_sys_dept" SET (autovacuum_enabled = true);
ALTER TABLE "s_manage"."tb_sys_menu" SET (autovacuum_enabled = true);
ALTER TABLE "s_manage"."tb_log_oper" SET (autovacuum_enabled = true);

-- 设置填充因子以优化更新性能 (为经常更新的表预留空间)
ALTER TABLE "s_manage"."tb_sys_user" SET (fillfactor = 90);
ALTER TABLE "s_manage"."tb_log_online" SET (fillfactor = 85);

-- 分析表以更新统计信息
ANALYZE "s_manage"."tb_sys_user";
ANALYZE "s_manage"."tb_sys_role";
ANALYZE "s_manage"."tb_sys_dept";
ANALYZE "s_manage"."tb_sys_menu";
ANALYZE "s_manage"."tb_dict_type";
ANALYZE "s_manage"."tb_dict_data";
